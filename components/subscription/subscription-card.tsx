import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import React from "react";
import { TouchableOpacity, View } from "react-native";

import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Text } from "@/components/ui/text";
import { Muted } from "@/components/ui/typography";
import {
	SUBSCRIPTION_PLANS,
	SubscriptionPlan,
	formatPrice,
} from "@/config/stripe";
import { colors } from "@/constants/colors";
import { useSubscription } from "@/hooks/useSubscription";
import { useStripePayment } from "@/hooks/useStripePayment";
import { useColorScheme } from "@/lib/useColorScheme";

interface SubscriptionCardProps {
	onUpgrade?: () => void;
}

export function SubscriptionCard({ onUpgrade }: SubscriptionCardProps) {
	const router = useRouter();
	const { actualColorScheme } = useColorScheme();
	const { subscription, currentPlan, loading: subscriptionLoading } = useSubscription();
	const { createSubscription, loading: paymentLoading } = useStripePayment();

	const isFreePlan = currentPlan === SubscriptionPlan.FREE;
	const planConfig = SUBSCRIPTION_PLANS[currentPlan];

	const handleUpgrade = async () => {
		if (onUpgrade) {
			onUpgrade();
			return;
		}

		// Navigate to full subscription page
		router.push("/subscription");
	};

	const handleQuickSubscribe = async () => {
		// Quick subscribe to monthly plan
		const monthlyPlan = SUBSCRIPTION_PLANS[SubscriptionPlan.MONTHLY];
		if (monthlyPlan.priceId) {
			const result = await createSubscription(monthlyPlan.priceId);
			if (result.success) {
				// Subscription successful, user will be redirected by Stripe
			}
		}
	};

	const getStatusColor = () => {
		if (!subscription) return "text-muted-foreground";
		
		switch (subscription.status) {
			case "active":
				return subscription.cancelAtPeriodEnd ? "text-orange-600" : "text-green-600";
			case "past_due":
				return "text-red-600";
			case "canceled":
				return "text-gray-600";
			case "trialing":
				return "text-blue-600";
			default:
				return "text-gray-600";
		}
	};

	const getStatusText = () => {
		if (!subscription) return "Free Plan";
		
		const currentPeriodEnd = new Date(subscription.currentPeriodEnd);
		
		if (subscription.cancelAtPeriodEnd) {
			return `Cancels on ${currentPeriodEnd.toLocaleDateString()}`;
		}
		
		switch (subscription.status) {
			case "active":
				return `Renews on ${currentPeriodEnd.toLocaleDateString()}`;
			case "past_due":
				return "Payment failed";
			case "canceled":
				return "Canceled";
			case "trialing":
				return `Trial ends on ${currentPeriodEnd.toLocaleDateString()}`;
			default:
				return subscription.status;
		}
	};

	if (subscriptionLoading) {
		return (
			<Card className="mb-4">
				<CardContent className="p-4">
					<Text className="text-center text-muted-foreground">Loading subscription...</Text>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card className="mb-4">
			<CardHeader>
				<View className="flex-row items-center justify-between">
					<View className="flex-1">
						<CardTitle className="text-lg">{planConfig.name}</CardTitle>
						<CardDescription className={getStatusColor()}>
							{getStatusText()}
						</CardDescription>
					</View>
					<View className="items-end">
						<Text className="text-lg font-semibold text-primary">
							{isFreePlan ? "Free" : formatPrice(planConfig.price)}
						</Text>
						{!isFreePlan && planConfig.interval && (
							<Muted className="text-xs">per {planConfig.interval}</Muted>
						)}
					</View>
				</View>
			</CardHeader>

			<CardContent>
				{/* Current Plan Features */}
				<View className="space-y-2 mb-4">
					{isFreePlan ? (
						<>
							<View className="flex-row items-center">
								<Ionicons
									name="checkmark-circle"
									size={16}
									color={
										actualColorScheme === "dark"
											? colors.dark.primary
											: colors.light.primary
									}
									style={{ marginRight: 8 }}
								/>
								<Text className="flex-1">5 exercises per day</Text>
							</View>
							<View className="flex-row items-center">
								<Ionicons
									name="close-circle"
									size={16}
									color={colors.light.destructive}
									style={{ marginRight: 8 }}
								/>
								<Text className="flex-1 text-muted-foreground">Unlimited exercises</Text>
							</View>
							<View className="flex-row items-center">
								<Ionicons
									name="close-circle"
									size={16}
									color={colors.light.destructive}
									style={{ marginRight: 8 }}
								/>
								<Text className="flex-1 text-muted-foreground">AI-powered feedback</Text>
							</View>
						</>
					) : (
						planConfig.features.slice(0, 3).map((feature, index) => (
							<View key={index} className="flex-row items-center">
								<Ionicons
									name="checkmark-circle"
									size={16}
									color={
										actualColorScheme === "dark"
											? colors.dark.primary
											: colors.light.primary
									}
									style={{ marginRight: 8 }}
								/>
								<Text className="flex-1">{feature}</Text>
							</View>
						))
					)}
				</View>

				{/* Action Buttons */}
				<View className="space-y-2">
					{isFreePlan ? (
						<>
							<Button
								variant="default"
								size="default"
								onPress={handleQuickSubscribe}
								disabled={paymentLoading}
								className="w-full"
							>
								<Text>
									{paymentLoading ? "Processing..." : "Upgrade to Premium"}
								</Text>
							</Button>
							<TouchableOpacity onPress={handleUpgrade}>
								<Text className="text-center text-primary text-sm">
									View all plans
								</Text>
							</TouchableOpacity>
						</>
					) : (
						<Button
							variant="outline"
							size="default"
							onPress={handleUpgrade}
							className="w-full"
						>
							<Text>Manage Subscription</Text>
						</Button>
					)}
				</View>

				{/* Payment Failed Warning */}
				{subscription?.status === "past_due" && (
					<View className="mt-4 p-3 bg-red-50 rounded-lg">
						<Text className="text-red-800 text-sm">
							Payment failed. Please update your payment method.
						</Text>
					</View>
				)}
			</CardContent>
		</Card>
	);
}
