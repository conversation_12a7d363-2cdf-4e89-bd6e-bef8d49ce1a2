import { useRouter } from "expo-router";
import React, { useState } from "react";
import { <PERSON><PERSON>, ScrollView, TouchableOpacity, View } from "react-native";

import { SafeAreaView } from "@/components/safe-area-view";
import { Button } from "@/components/ui/button";
import { Text } from "@/components/ui/text";
import { H1, H2, Muted } from "@/components/ui/typography";
import { useAuth } from "@/context/supabase-provider";
import { useI18n, useSettingsTranslation } from "@/hooks/useI18n";
import { useColorScheme } from "@/lib/useColorScheme";
import { cn } from "@/lib/utils";

export default function Settings() {
	const router = useRouter();
	const { signOut } = useAuth();
	const {
		colorScheme,
		setColorScheme,
		isLoading: isThemeLoading,
	} = useColorScheme();
	const { t: tSettings } = useSettingsTranslation();
	const { changeLanguage, getCurrentLanguage, getSupportedLanguages } =
		useI18n();
	const [isChangingLanguage, setIsChangingLanguage] = useState(false);

	const currentLanguage = getCurrentLanguage();
	const supportedLanguages = getSupportedLanguages();

	const themeOptions = [
		{ value: "system", label: tSettings("appearance.theme.options.system") },
		{ value: "light", label: tSettings("appearance.theme.options.light") },
		{ value: "dark", label: tSettings("appearance.theme.options.dark") },
	];

	const handleLanguageChange = async (languageCode: string) => {
		if (languageCode === currentLanguage) return;

		setIsChangingLanguage(true);
		try {
			await changeLanguage(languageCode);
			Alert.alert(
				tSettings("dialogs.languageChanged.title"),
				tSettings("dialogs.languageChanged.message"),
				[{ text: tSettings("dialogs.languageChanged.button") }],
			);
		} catch (error) {
			console.error("Error changing language:", error);
		} finally {
			setIsChangingLanguage(false);
		}
	};

	const handleSignOut = () => {
		Alert.alert(
			tSettings("dialogs.signOut.title"),
			tSettings("dialogs.signOut.message"),
			[
				{
					text: tSettings("dialogs.signOut.buttons.cancel"),
					style: "cancel",
				},
				{
					text: tSettings("dialogs.signOut.buttons.signOut"),
					style: "destructive",
					onPress: async () => {
						await signOut();
					},
				},
			],
		);
	};

	return (
		<SafeAreaView className="flex-1 bg-background">
			<ScrollView className="flex-1 p-4">
				<H1 className="mb-6">{tSettings("title")}</H1>

				{/* Appearance Section */}
				<View className="mb-8">
					<H2 className="mb-4">{tSettings("sections.appearance")}</H2>

					{/* Theme Selection */}
					<View className="mb-6">
						<Text className="text-lg font-semibold mb-2">
							{tSettings("appearance.theme.title")}
						</Text>
						<Muted className="mb-4">
							{tSettings("appearance.theme.description")}
						</Muted>
						<View className="gap-y-2">
							{themeOptions.map((option) => (
								<TouchableOpacity
									key={option.value}
									onPress={() =>
										setColorScheme(option.value as "light" | "dark" | "system")
									}
									disabled={isThemeLoading}
									className={cn(
										"flex flex-row items-center justify-between p-4 rounded-lg border",
										colorScheme === option.value
											? "border-primary bg-primary/10"
											: "border-border bg-card",
										isThemeLoading && "opacity-50",
									)}
								>
									<Text
										className={cn(
											"text-base",
											colorScheme === option.value
												? "text-primary font-medium"
												: "text-foreground",
										)}
									>
										{option.label}
									</Text>
									{colorScheme === option.value && (
										<Text className="text-primary">✓</Text>
									)}
								</TouchableOpacity>
							))}
						</View>
					</View>
				</View>

				{/* Language Section */}
				<View className="mb-8">
					<H2 className="mb-4">{tSettings("sections.language")}</H2>

					{/* Language Selection */}
					<View className="mb-6">
						<Text className="text-lg font-semibold mb-2">
							{tSettings("language.appLanguage.title")}
						</Text>
						<Muted className="mb-4">
							{tSettings("language.appLanguage.description")}
						</Muted>
						<View className="gap-y-2">
							{supportedLanguages.map((language) => (
								<TouchableOpacity
									key={language.code}
									onPress={() => handleLanguageChange(language.code)}
									disabled={isChangingLanguage}
									className={cn(
										"flex flex-row items-center justify-between p-4 rounded-lg border",
										currentLanguage === language.code
											? "border-primary bg-primary/10"
											: "border-border bg-card",
										isChangingLanguage && "opacity-50",
									)}
								>
									<View>
										<Text
											className={cn(
												"text-base",
												currentLanguage === language.code
													? "text-primary font-medium"
													: "text-foreground",
											)}
										>
											{language.nativeName}
										</Text>
										<Text className="text-sm text-muted-foreground">
											{language.name}
										</Text>
									</View>
									{currentLanguage === language.code && (
										<Text className="text-primary">✓</Text>
									)}
								</TouchableOpacity>
							))}
						</View>
					</View>
				</View>

				{/* Account Section */}
				<View className="mb-8">
					<H2 className="mb-4">{tSettings("sections.account")}</H2>

					<View className="gap-y-2">
						<TouchableOpacity className="flex flex-row items-center justify-between p-4 rounded-lg border border-border bg-card">
							<Text className="text-base">{tSettings("account.profile")}</Text>
							<Text className="text-muted-foreground">→</Text>
						</TouchableOpacity>

						<TouchableOpacity
							onPress={() => router.push("/subscription")}
							className="flex flex-row items-center justify-between p-4 rounded-lg border border-border bg-card"
						>
							<Text className="text-base">
								{tSettings("account.subscription")}
							</Text>
							<Text className="text-muted-foreground">→</Text>
						</TouchableOpacity>

						<TouchableOpacity className="flex flex-row items-center justify-between p-4 rounded-lg border border-border bg-card">
							<Text className="text-base">{tSettings("account.privacy")}</Text>
							<Text className="text-muted-foreground">→</Text>
						</TouchableOpacity>
					</View>
				</View>

				{/* Support Section */}
				<View className="mb-8">
					<H2 className="mb-4">{tSettings("sections.support")}</H2>

					<View className="gap-y-2">
						<TouchableOpacity className="flex flex-row items-center justify-between p-4 rounded-lg border border-border bg-card">
							<Text className="text-base">{tSettings("support.help")}</Text>
							<Text className="text-muted-foreground">→</Text>
						</TouchableOpacity>

						<TouchableOpacity className="flex flex-row items-center justify-between p-4 rounded-lg border border-border bg-card">
							<Text className="text-base">{tSettings("support.feedback")}</Text>
							<Text className="text-muted-foreground">→</Text>
						</TouchableOpacity>

						<TouchableOpacity className="flex flex-row items-center justify-between p-4 rounded-lg border border-border bg-card">
							<Text className="text-base">{tSettings("support.about")}</Text>
							<Text className="text-muted-foreground">→</Text>
						</TouchableOpacity>
					</View>
				</View>

				{/* Sign Out Button */}
				<Button
					size="default"
					variant="destructive"
					onPress={handleSignOut}
					className="mb-6"
				>
					<Text>{tSettings("account.signOut")}</Text>
				</Button>
			</ScrollView>
		</SafeAreaView>
	);
}
