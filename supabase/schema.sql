-- DutchEase Database Schema
-- This file contains the complete database schema for the DutchEase application

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create custom types/enums
CREATE TYPE difficulty_level AS ENUM ('A1', 'A2', 'B1', 'B2', 'C1');
CREATE TYPE exercise_type AS ENUM ('reading', 'writing', 'listening', 'speaking');
CREATE TYPE subscription_plan AS ENUM ('free', 'monthly', '3_months', '12_months');

-- Profiles table (extends auth.users)
CREATE TABLE public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    native_language TEXT,
    current_level difficulty_level DEFAULT 'A2',
    learning_goals TEXT[] DEFAULT '{}', -- Array of learning goal IDs
    daily_study_time TEXT, -- Study time preference (e.g., '15-20', '30-45')
    streak_count INTEGER DEFAULT 0,
    total_exercises INTEGER DEFAULT 0,
    completed_exercises INTEGER DEFAULT 0,
    subscription_plan subscription_plan DEFAULT 'free',
    subscription_expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Exercises table
CREATE TABLE public.exercises (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    type exercise_type NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    content JSONB NOT NULL, -- Flexible content structure
    difficulty difficulty_level NOT NULL,
    duration_minutes INTEGER NOT NULL DEFAULT 15,
    is_active BOOLEAN DEFAULT true,
    tags TEXT[] DEFAULT '{}',
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User progress tracking
CREATE TABLE public.user_progress (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    exercise_id UUID REFERENCES public.exercises(id) ON DELETE CASCADE NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    score DECIMAL(5,2), -- Score out of 100
    time_spent_minutes INTEGER NOT NULL,
    answers JSONB NOT NULL, -- User's answers and responses
    feedback JSONB, -- AI-generated feedback
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure one progress record per user per exercise
    UNIQUE(user_id, exercise_id)
);

-- Daily streaks tracking
CREATE TABLE public.daily_streaks (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    date DATE NOT NULL,
    exercises_completed INTEGER DEFAULT 0,
    time_spent_minutes INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure one record per user per day
    UNIQUE(user_id, date)
);

-- Subscriptions table
CREATE TABLE public.subscriptions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    stripe_subscription_id TEXT UNIQUE,
    stripe_customer_id TEXT,
    stripe_price_id TEXT,
    plan_type subscription_plan NOT NULL,
    status TEXT NOT NULL, -- active, canceled, past_due, incomplete, trialing, etc.
    current_period_start TIMESTAMP WITH TIME ZONE,
    current_period_end TIMESTAMP WITH TIME ZONE,
    cancel_at_period_end BOOLEAN DEFAULT false,
    canceled_at TIMESTAMP WITH TIME ZONE,
    trial_start TIMESTAMP WITH TIME ZONE,
    trial_end TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE UNIQUE INDEX idx_subscriptions_user_active
ON public.subscriptions(user_id)
WHERE status = 'active';

-- Exercise categories for organization
CREATE TABLE public.exercise_categories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    icon TEXT, -- Icon name or URL
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Junction table for exercise categories
CREATE TABLE public.exercise_category_mappings (
    exercise_id UUID REFERENCES public.exercises(id) ON DELETE CASCADE,
    category_id UUID REFERENCES public.exercise_categories(id) ON DELETE CASCADE,
    PRIMARY KEY (exercise_id, category_id)
);

-- User achievements/badges
CREATE TABLE public.user_achievements (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    achievement_type TEXT NOT NULL, -- 'streak', 'exercises_completed', 'perfect_score', etc.
    achievement_value INTEGER, -- The value that triggered the achievement
    earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Prevent duplicate achievements
    UNIQUE(user_id, achievement_type, achievement_value)
);

-- Create indexes for better performance
CREATE INDEX idx_profiles_email ON public.profiles(email);
CREATE INDEX idx_exercises_type ON public.exercises(type);
CREATE INDEX idx_exercises_difficulty ON public.exercises(difficulty);
CREATE INDEX idx_exercises_active ON public.exercises(is_active);
CREATE INDEX idx_user_progress_user_id ON public.user_progress(user_id);
CREATE INDEX idx_user_progress_exercise_id ON public.user_progress(exercise_id);
CREATE INDEX idx_user_progress_completed_at ON public.user_progress(completed_at);
CREATE INDEX idx_daily_streaks_user_date ON public.daily_streaks(user_id, date);
CREATE INDEX idx_subscriptions_user_id ON public.subscriptions(user_id);
CREATE INDEX idx_subscriptions_stripe_id ON public.subscriptions(stripe_subscription_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_exercises_updated_at BEFORE UPDATE ON public.exercises
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON public.subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update user subscription plan in profiles table
CREATE OR REPLACE FUNCTION update_user_subscription_plan()
RETURNS TRIGGER AS $$
BEGIN
    -- Update the user's subscription plan in profiles table
    UPDATE public.profiles
    SET
        subscription_plan = NEW.plan_type,
        subscription_expires_at = NEW.current_period_end,
        updated_at = NOW()
    WHERE id = NEW.user_id;

    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update profile when subscription changes
CREATE TRIGGER update_profile_subscription AFTER INSERT OR UPDATE ON public.subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_user_subscription_plan();

-- Function to check if user has feature access
CREATE OR REPLACE FUNCTION user_has_feature_access(user_uuid UUID, feature_name TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    user_plan subscription_plan;
BEGIN
    -- Get user's current subscription plan
    SELECT subscription_plan INTO user_plan
    FROM public.profiles
    WHERE id = user_uuid;

    -- Default to free if no plan found
    IF user_plan IS NULL THEN
        user_plan := 'free';
    END IF;

    -- Check feature access based on plan
    CASE feature_name
        WHEN 'unlimited_exercises' THEN
            RETURN user_plan != 'free';
        WHEN 'mock_exams' THEN
            RETURN user_plan != 'free';
        WHEN 'ai_features' THEN
            RETURN user_plan != 'free';
        WHEN 'offline_mode' THEN
            RETURN user_plan != 'free';
        WHEN 'advanced_analytics' THEN
            RETURN user_plan != 'free';
        ELSE
            RETURN true; -- Default to allowing access for unknown features
    END CASE;
END;
$$ language 'plpgsql';
