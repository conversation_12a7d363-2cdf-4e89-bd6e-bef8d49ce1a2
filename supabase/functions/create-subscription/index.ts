import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import Stripe from "https://esm.sh/stripe@14.21.0";

const stripe = new Stripe(Deno.env.get("STRIPE_SECRET_KEY") || "", {
	apiVersion: "2023-10-16",
	httpClient: Stripe.createFetchHttpClient(),
});

const supabaseUrl = Deno.env.get("SUPABASE_URL")!;
const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

serve(async (req) => {
	if (req.method !== "POST") {
		return new Response("Method not allowed", { status: 405 });
	}

	try {
		const {
			priceId,
			userId,
			email,
		}: { priceId: string; userId: string; email: string } = await req.json();

		if (!priceId || !userId || !email) {
			return new Response("Missing required fields", { status: 400 });
		}

		// Get or create Stripe customer
		let customer: Stripe.Customer;

		// Check if customer already exists
		const { data: existingSubscription, error: subscriptionError } =
			await supabase
				.from("subscriptions")
				.select("stripe_customer_id")
				.eq("user_id", userId)
				.single();

		if (subscriptionError && subscriptionError.code !== "PGRST116") {
			// PGRST116 = no rows returned, which is fine for new users
			console.error("Error retrieving subscription:", subscriptionError);
			return new Response("Error retrieving subscription", { status: 500 });
		}

		if (existingSubscription?.stripe_customer_id) {
			customer = (await stripe.customers.retrieve(
				existingSubscription.stripe_customer_id,
			)) as Stripe.Customer;
		} else {
			// Create new customer
			customer = await stripe.customers.create({
				email,
				metadata: {
					user_id: userId,
				},
			});
		}

		// Create subscription
		const subscription = await stripe.subscriptions.create({
			customer: customer.id,
			items: [{ price: priceId }],
			payment_behavior: "default_incomplete",
			payment_settings: { save_default_payment_method: "on_subscription" },
			expand: ["latest_invoice.payment_intent"],
		});

		const invoice = subscription.latest_invoice as Stripe.Invoice;
		const paymentIntent = invoice.payment_intent as Stripe.PaymentIntent;

		// Store subscription in database
		const { error } = await supabase.from("subscriptions").upsert(
			{
				user_id: userId,
				stripe_subscription_id: subscription.id,
				stripe_customer_id: customer.id,
				stripe_price_id: priceId,
				plan_type: getPlanTypeFromPriceId(priceId),
				status: subscription.status,
				current_period_start: new Date(
					subscription.current_period_start * 1000,
				).toISOString(),
				current_period_end: new Date(
					subscription.current_period_end * 1000,
				).toISOString(),
				cancel_at_period_end: subscription.cancel_at_period_end,
				trial_start: subscription.trial_start
					? new Date(subscription.trial_start * 1000).toISOString()
					: null,
				trial_end: subscription.trial_end
					? new Date(subscription.trial_end * 1000).toISOString()
					: null,
			},
			{
				onConflict: "stripe_subscription_id",
				ignoreDuplicates: false,
			},
		);

		if (error) {
			console.error("Error storing subscription:", error);
			// Cancel the Stripe subscription if database update fails
			await stripe.subscriptions.cancel(subscription.id);
			throw error;
		}

		return new Response(
			JSON.stringify({
				subscriptionId: subscription.id,
				clientSecret: paymentIntent.client_secret,
				customerId: customer.id,
			}),
			{
				headers: { "Content-Type": "application/json" },
				status: 200,
			},
		);
	} catch (error: any) {
		console.error("Error creating subscription:", error);
		return new Response(JSON.stringify({ error: error.message }), {
			headers: { "Content-Type": "application/json" },
			status: 500,
		});
	}
});

function getPlanTypeFromPriceId(priceId: string): string {
	if (priceId === Deno.env.get("STRIPE_PRICE_ID_MONTHLY")) {
		return "monthly";
	} else if (priceId === Deno.env.get("STRIPE_PRICE_ID_3_MONTHS")) {
		return "3_months";
	} else if (priceId === Deno.env.get("STRIPE_PRICE_ID_12_MONTHS")) {
		return "12_months";
	}
	return "free";
}
